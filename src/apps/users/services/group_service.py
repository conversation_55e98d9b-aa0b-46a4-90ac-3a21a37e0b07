import logging
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db import transaction
from django.utils import timezone
from ..models import Group, GroupMembership
from ..exceptions.exceptions import (
    GroupNotFoundException,
    GroupAlreadyExistsException,
    GroupPermissionException,
    GroupMembershipException,
    GroupValidationException
)

User = get_user_model()
logger = logging.getLogger(__name__)


class GroupService:
    
    @staticmethod
    def create_group(name, description, created_by):
        try:
            if Group.objects.filter(name=name, is_active=True).exists():
                raise GroupAlreadyExistsException(
                    message=f"Group with name '{name}' already exists"
                )
            
            with transaction.atomic():
                group = Group.objects.create(
                    name=name,
                    description=description,
                    created_by=created_by
                )
                
                group.add_member(created_by, role='admin')
                
                logger.info(f"Group created successfully: {group.name} by {created_by.username}")
                return group
                
        except GroupAlreadyExistsException:
            raise
        except Exception as e:
            logger.error(f"Error creating group: {str(e)}")
            raise GroupValidationException(
                message="Failed to create group",
                code="GROUP_CREATION_FAILED"
            )

    @staticmethod
    def create_group_with_permissions(name, permissions, created_by):
        try:
            if Group.objects.filter(name=name, is_active=True).exists():
                raise GroupAlreadyExistsException(
                    message=f"Group with name '{name}' already exists"
                )

            with transaction.atomic():
                group = Group.objects.create(
                    name=name,
                    created_by=created_by
                )
                if permissions:
                    group.permissions.set(permissions)
                group.add_member(created_by, role='admin')
                return group

        except GroupAlreadyExistsException:
            raise
        except Exception as e:
            logger.error(f"Error creating group with permissions: {str(e)}")
            raise GroupValidationException(
                message="Failed to create group with permissions",
                code="GROUP_CREATION_FAILED"
            )
    
    @staticmethod
    def get_group_by_id(group_id):
        try:
            group = Group.objects.get(id=group_id, is_active=True)
            return group
        except Group.DoesNotExist:
            raise GroupNotFoundException(
                message=f"Group with ID {group_id} not found"
            )
    
    @staticmethod
    def get_all_groups(include_inactive=False):
        try:
            if include_inactive:
                return Group.objects.all().order_by('-date_created')
            return Group.objects.filter(is_active=True).order_by('-date_created')
        except Exception as e:
            raise GroupValidationException(
                message="Failed to retrieve groups"
            )
    
    @staticmethod
    def get_user_groups(user_id):
        try:
            user = User.objects.get(id=user_id, is_active=True)
            return Group.objects.filter(
                members=user,
                groupmembership__is_active=True,
                is_active=True
            ).order_by('-date_created')
        except User.DoesNotExist:
            raise GroupNotFoundException(
                message=f"User with ID {user_id} not found"
            )
        except Exception as e:
            logger.error(f"Error retrieving user groups: {str(e)}")
            raise GroupValidationException(
                message="Failed to retrieve user groups"
            )
    
    @staticmethod
    def update_group(group_id, name=None, description=None, user=None):
        try:
            group = GroupService.get_group_by_id(group_id)
            if user and not GroupService.is_group_admin(group, user):
                raise GroupPermissionException(
                    message="Only group admins can update group information"
                )
            if name and name != group.name:
                if Group.objects.filter(name=name, is_active=True).exclude(id=group_id).exists():
                    raise GroupAlreadyExistsException(
                        message=f"Group with name '{name}' already exists"
                    )
                group.name = name
            
            if description is not None:
                group.description = description
            group.save()
            return group
            
        except (GroupNotFoundException, GroupAlreadyExistsException, GroupPermissionException):
            raise
        except Exception as e:
            logger.error(f"Error updating group: {str(e)}")
            raise GroupValidationException(
                message="Failed to update group"
            )

    @staticmethod
    def update_group_with_permissions(group_id, name=None, permissions=None, user=None):
        try:
            group = GroupService.get_group_by_id(group_id)
            if user and not GroupService.is_group_admin(group, user):
                raise GroupPermissionException(
                    message="Only group admins can update group information"
                )
            if name and name != group.name:
                if Group.objects.filter(name=name, is_active=True).exclude(id=group_id).exists():
                    raise GroupAlreadyExistsException(
                        message=f"Group with name '{name}' already exists"
                    )
                group.name = name

            if permissions is not None:
                group.permissions.set(permissions)

            group.save()
            logger.info(f"Group with permissions updated successfully: {group.name}")
            return group

        except (GroupNotFoundException, GroupAlreadyExistsException, GroupPermissionException):
            raise
        except Exception as e:
            logger.error(f"Error updating group with permissions: {str(e)}")
            raise GroupValidationException(
                message="Failed to update group with permissions"
            )

    @staticmethod
    def delete_group(group_id, user=None):
        try:
            group = GroupService.get_group_by_id(group_id)
            if user and not GroupService.is_group_admin(group, user):
                raise GroupPermissionException(
                    message="Only group admins can delete the group"
                )
            
            group.soft_delete()
            logger.info(f"Group deleted successfully: {group.name}")
            return group
            
        except (GroupNotFoundException, GroupPermissionException):
            raise
        except Exception as e:
            logger.error(f"Error deleting group: {str(e)}")
            raise GroupValidationException(
                message="Failed to delete group"
            )
    
    @staticmethod
    def add_member(group_id, user_id, role='member', requesting_user=None):
        try:
            group = GroupService.get_group_by_id(group_id)
            user = User.objects.get(id=user_id, is_active=True)
            if requesting_user and not GroupService.is_group_admin(group, requesting_user):
                raise GroupPermissionException(
                    message="Only group admins can add members"
                )
            if GroupMembership.objects.filter(
                group=group, 
                user=user, 
                is_active=True
            ).exists():
                raise GroupMembershipException(
                    message="User is already a member of this group"
                )
            
            membership = group.add_member(user, role)
            logger.info(f"User {user.username} added to group {group.name} as {role}")
            return membership
            
        except (GroupNotFoundException, GroupPermissionException, GroupMembershipException):
            raise
        except User.DoesNotExist:
            raise GroupNotFoundException(
                message=f"User with ID {user_id} not found"
            )
        except Exception as e:
            logger.error(f"Error adding member to group: {str(e)}")
            raise GroupMembershipException(
                message="Failed to add member to group"
            )
    
    @staticmethod
    def remove_member(group_id, user_id, requesting_user=None):
        try:
            group = GroupService.get_group_by_id(group_id)
            user = User.objects.get(id=user_id)
            if requesting_user and not GroupService.is_group_admin(group, requesting_user):
                if requesting_user.id != user.id:
                    raise GroupPermissionException(
                        message="Only group admins can remove members"
                    )
            
            # Check if user is a member
            membership = GroupMembership.objects.filter(
                group=group, 
                user=user, 
                is_active=True
            ).first()
            
            if not membership:
                raise GroupMembershipException(
                    message="User is not a member of this group"
                )
            
            # Prevent removing the last admin
            if membership.role == 'admin':
                admin_count = GroupMembership.objects.filter(
                    group=group, 
                    role='admin',
                    is_active=True
                ).count()
                if admin_count <= 1:
                    raise GroupMembershipException(
                        message="Cannot remove the last admin from the group"
                    )
            
            group.remove_member(user)
            logger.info(f"User {user.username} removed from group {group.name}")
            return True
            
        except (GroupNotFoundException, GroupPermissionException, GroupMembershipException):
            raise
        except User.DoesNotExist:
            raise GroupNotFoundException(
                message=f"User with ID {user_id} not found"
            )
        except Exception as e:
            logger.error(f"Error removing member from group: {str(e)}")
            raise GroupMembershipException(
                message="Failed to remove member from group"
            )
    
    @staticmethod
    def update_member_role(group_id, user_id, new_role, requesting_user=None):
        """Update a member's role in the group"""
        try:
            group = GroupService.get_group_by_id(group_id)
            user = User.objects.get(id=user_id)
            
            # Check permissions
            if requesting_user and not GroupService.is_group_admin(group, requesting_user):
                raise GroupPermissionException(
                    message="Only group admins can update member roles"
                )
            
            membership = GroupMembership.objects.filter(
                group=group, 
                user=user, 
                is_active=True
            ).first()
            
            if not membership:
                raise GroupMembershipException(
                    message="User is not a member of this group"
                )
            
            # Prevent demoting the last admin
            if membership.role == 'admin' and new_role != 'admin':
                admin_count = GroupMembership.objects.filter(
                    group=group, 
                    role='admin',
                    is_active=True
                ).count()
                if admin_count <= 1:
                    raise GroupMembershipException(
                        message="Cannot demote the last admin of the group"
                    )
            
            membership.role = new_role
            membership.save()
            
            logger.info(f"User {user.username} role updated to {new_role} in group {group.name}")
            return membership
            
        except (GroupNotFoundException, GroupPermissionException, GroupMembershipException):
            raise
        except User.DoesNotExist:
            raise GroupNotFoundException(
                message=f"User with ID {user_id} not found"
            )
        except Exception as e:
            logger.error(f"Error updating member role: {str(e)}")
            raise GroupMembershipException(
                message="Failed to update member role"
            )
    
    @staticmethod
    def is_group_admin(group, user):
        """Check if user is an admin of the group"""
        return GroupMembership.objects.filter(
            group=group,
            user=user,
            role='admin',
            is_active=True
        ).exists()
    
    @staticmethod
    def is_group_member(group, user):
        """Check if user is a member of the group"""
        return GroupMembership.objects.filter(
            group=group,
            user=user,
            is_active=True
        ).exists()

    @staticmethod
    def get_all_permissions():
        """Get all available permissions in the system"""
        try:
            permissions = Permission.objects.all().select_related('content_type')
            logger.info(f"Retrieved {permissions.count()} permissions")
            return permissions
        except Exception as e:
            logger.error(f"Error retrieving permissions: {str(e)}")
            raise GroupValidationException(
                message="Failed to retrieve permissions"
            )
