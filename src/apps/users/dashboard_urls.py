from django.urls import path
from .views.user_views import GetAllUsersView, GetUserByIdView, ModifyView
from .views.group_views import (
    CreateGroupView, UpdateGroupView, GetAllGroupsView,
    GetGroupByIdView, DeleteGroupView, GetUserGroupsView,
    GetAllPermissionsView, CreateGroupWithPermissionsView,
    UpdateGroupWithPermissionsView, GetAllGroupsWithPermissionsView,
    GetGroupWithPermissionsByIdView
)

# Dashboard URLs - Admin Only Access (راوتات الداش بورد للأدمن فقط)
urlpatterns = [
    # User Management Routes (إدارة المستخدمين)
    path("users/", GetAllUsersView.as_view(), name="dashboard_get_all_users"),
    path("users/<uuid:id>/", GetUserByIdView.as_view(), name="dashboard_get_user_by_id"),
    path("users/<uuid:id>/update/", ModifyView.as_view(), name="dashboard_update_user"),

    # Group Management Routes (إدارة المجموعات)
    path("groups/", GetAllGroupsView.as_view(), name="dashboard_get_all_groups"),
    path("groups/create/", CreateGroupView.as_view(), name="dashboard_create_group"),
    path("groups/<uuid:id>/", GetGroupByIdView.as_view(), name="dashboard_get_group_by_id"),
    path("groups/<uuid:id>/update/", UpdateGroupView.as_view(), name="dashboard_update_group"),
    path("groups/<uuid:id>/delete/", DeleteGroupView.as_view(), name="dashboard_delete_group"),

    # Group with Permissions Routes (المجموعات مع الصلاحيات)
    path("groups/with-permissions/", CreateGroupWithPermissionsView.as_view(), name="dashboard_create_group_with_permissions"),
    path("groups/with-permissions/all/", GetAllGroupsWithPermissionsView.as_view(), name="dashboard_get_all_groups_with_permissions"),
    path("groups/with-permissions/<uuid:id>/", GetGroupWithPermissionsByIdView.as_view(), name="dashboard_get_group_with_permissions_by_id"),
    path("groups/with-permissions/<uuid:id>/update/", UpdateGroupWithPermissionsView.as_view(), name="dashboard_update_group_with_permissions"),

    # Permission Management Routes (إدارة الصلاحيات)
    path("permissions/", GetAllPermissionsView.as_view(), name="dashboard_get_all_permissions"),

    # User Groups Management (إدارة مجموعات المستخدمين)
    path("users/<uuid:user_id>/groups/", GetUserGroupsView.as_view(), name="dashboard_get_user_groups"),
]
