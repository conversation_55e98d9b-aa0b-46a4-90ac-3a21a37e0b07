from django.urls import path, include
from .views.dashboard_views import (
    # User Management Views
    DashboardGetAllUsersView, DashboardGetUserByIdView, 
    DashboardCreateUserView, DashboardUpdateUserView, 
    DashboardDeleteUserView, DashboardRestoreUserView,
    DashboardUpdateUserPermissionsView, DashboardGetUserPermissionsView,
    
    # Group Management Views  
    DashboardGetAllGroupsView, DashboardGetGroupByIdView,
    DashboardCreateGroupView, DashboardUpdateGroupView,
    DashboardDeleteGroupView, DashboardRestoreGroupView,
    DashboardAddUserToGroupView, DashboardRemoveUserFromGroupView,
    DashboardGetGroupMembersView, DashboardUpdateGroupPermissionsView,
    
    # Permission Management Views
    DashboardGetAllPermissionsView, DashboardGetPermissionsByContentTypeView,
    
    # Statistics and Analytics Views
    DashboardStatsView, DashboardUserStatsView, DashboardGroupStatsView,
    
    # Bulk Operations Views
    DashboardBulkUserOperationsView, DashboardBulkGroupOperationsView
)

# Dashboard URLs - Admin Only Access
urlpatterns = [
    # Dashboard Home & Stats
    path("", DashboardStatsView.as_view(), name="dashboard_stats"),
    path("stats/users/", DashboardUserStatsView.as_view(), name="dashboard_user_stats"),
    path("stats/groups/", DashboardGroupStatsView.as_view(), name="dashboard_group_stats"),
    
    # User Management Routes
    path("users/", DashboardGetAllUsersView.as_view(), name="dashboard_get_all_users"),
    path("users/create/", DashboardCreateUserView.as_view(), name="dashboard_create_user"),
    path("users/<uuid:id>/", DashboardGetUserByIdView.as_view(), name="dashboard_get_user_by_id"),
    path("users/<uuid:id>/update/", DashboardUpdateUserView.as_view(), name="dashboard_update_user"),
    path("users/<uuid:id>/delete/", DashboardDeleteUserView.as_view(), name="dashboard_delete_user"),
    path("users/<uuid:id>/restore/", DashboardRestoreUserView.as_view(), name="dashboard_restore_user"),
    path("users/<uuid:id>/permissions/", DashboardGetUserPermissionsView.as_view(), name="dashboard_get_user_permissions"),
    path("users/<uuid:id>/permissions/update/", DashboardUpdateUserPermissionsView.as_view(), name="dashboard_update_user_permissions"),
    
    # Group Management Routes
    path("groups/", DashboardGetAllGroupsView.as_view(), name="dashboard_get_all_groups"),
    path("groups/create/", DashboardCreateGroupView.as_view(), name="dashboard_create_group"),
    path("groups/<uuid:id>/", DashboardGetGroupByIdView.as_view(), name="dashboard_get_group_by_id"),
    path("groups/<uuid:id>/update/", DashboardUpdateGroupView.as_view(), name="dashboard_update_group"),
    path("groups/<uuid:id>/delete/", DashboardDeleteGroupView.as_view(), name="dashboard_delete_group"),
    path("groups/<uuid:id>/restore/", DashboardRestoreGroupView.as_view(), name="dashboard_restore_group"),
    path("groups/<uuid:id>/members/", DashboardGetGroupMembersView.as_view(), name="dashboard_get_group_members"),
    path("groups/<uuid:id>/members/add/", DashboardAddUserToGroupView.as_view(), name="dashboard_add_user_to_group"),
    path("groups/<uuid:id>/members/<uuid:user_id>/remove/", DashboardRemoveUserFromGroupView.as_view(), name="dashboard_remove_user_from_group"),
    path("groups/<uuid:id>/permissions/update/", DashboardUpdateGroupPermissionsView.as_view(), name="dashboard_update_group_permissions"),
    
    # Permission Management Routes
    path("permissions/", DashboardGetAllPermissionsView.as_view(), name="dashboard_get_all_permissions"),
    path("permissions/content-type/<int:content_type_id>/", DashboardGetPermissionsByContentTypeView.as_view(), name="dashboard_get_permissions_by_content_type"),
    
    # Bulk Operations Routes
    path("bulk/users/", DashboardBulkUserOperationsView.as_view(), name="dashboard_bulk_user_operations"),
    path("bulk/groups/", DashboardBulkGroupOperationsView.as_view(), name="dashboard_bulk_group_operations"),
]
