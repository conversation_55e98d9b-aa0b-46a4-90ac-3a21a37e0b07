from django.urls import path
from .views.group_views import (
    CreateGroupView, UpdateGroupView, GetAllGroupsView,
    GetGroupByIdView, DeleteGroupView, GetUserGroupsView,
    GetAllPermissionsView, CreateGroupWithPermissionsView,
    UpdateGroupWithPermissionsView, GetAllGroupsWithPermissionsView,
    GetGroupWithPermissionsByIdView
)
from .views.group_member_views import (
    AddGroupMemberView, UpdateGroupMemberView, RemoveGroupMemberView,
    RemoveGroupMemberByBodyView, GetGroupMembersView
)

urlpatterns = [
    path("", CreateGroupView.as_view(), name="create_group"),
    path("all/", GetAllGroupsView.as_view(), name="get_all_groups"),
    path("<uuid:id>/", GetGroupByIdView.as_view(), name="get_group_by_id"),
    path("<uuid:id>/update/", UpdateGroupView.as_view(), name="update_group"),
    path("<uuid:id>/delete/", DeleteGroupView.as_view(), name="delete_group"),

    path("with-permissions/", CreateGroupWithPermissionsView.as_view(), name="create_group_with_permissions"),
    path("with-permissions/<uuid:id>/", GetGroupWithPermissionsByIdView.as_view(), name="get_group_with_permissions_by_id"),
    path("with-permissions/<uuid:id>/update/", UpdateGroupWithPermissionsView.as_view(), name="update_group_with_permissions"),

    path("permissions/", GetAllPermissionsView.as_view(), name="get_all_permissions"),


    path("users/<uuid:user_id>/", GetUserGroupsView.as_view(), name="get_user_groups"),
]
