from django.urls import path
from .views.group_views import (
    CreateGroupView, UpdateGroupView, GetAllGroupsView,
    GetGroupByIdView, DeleteGroupView, GetUserGroupsView,
    CreateGroupWithPermissionsView, UpdateGroupWithPermissionsView, 
    GetAllGroupsWithPermissionsView, GetGroupWithPermissionsByIdView
)

# Dashboard Groups Management URLs - Admin Only Access
# راوتات إدارة المجموعات في الداش بورد - للأدمن فقط
urlpatterns = [
    # Basic Group Management Routes (إدارة المجموعات الأساسية)
    path("", GetAllGroupsView.as_view(), name="dashboard_get_all_groups"),
    path("create/", CreateGroupView.as_view(), name="dashboard_create_group"),
    path("<uuid:id>/", GetGroupByIdView.as_view(), name="dashboard_get_group_by_id"),
    path("<uuid:id>/update/", UpdateGroupView.as_view(), name="dashboard_update_group"),
    path("<uuid:id>/delete/", DeleteGroupView.as_view(), name="dashboard_delete_group"),
    
    # Groups with Permissions Routes (المجموعات مع الصلاحيات)
    path("with-permissions/", CreateGroupWithPermissionsView.as_view(), name="dashboard_create_group_with_permissions"),
    path("with-permissions/all/", GetAllGroupsWithPermissionsView.as_view(), name="dashboard_get_all_groups_with_permissions"),
    path("with-permissions/<uuid:id>/", GetGroupWithPermissionsByIdView.as_view(), name="dashboard_get_group_with_permissions_by_id"),
    path("with-permissions/<uuid:id>/update/", UpdateGroupWithPermissionsView.as_view(), name="dashboard_update_group_with_permissions"),
    
    # User Groups Management (إدارة مجموعات المستخدمين)
    path("users/<uuid:user_id>/", GetUserGroupsView.as_view(), name="dashboard_get_user_groups"),
]
