import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..models import Group, GroupMembership
from ..exceptions.exceptions import (
    GroupException,
    GroupNotFoundException,
    GroupPermissionException,
    GroupMembershipException
)
from ..serializers.group_serializers import (
    GroupCreateSerializer,
    GroupUpdateSerializer,
    GroupListSerializer,
    GroupDetailSerializer,
    GroupMemberAddSerializer,
    GroupMemberUpdateSerializer,
    GroupMemberRemoveSerializer,
    GroupWithPermissionsCreateSerializer,
    GroupWithPermissionsUpdateSerializer,
    GroupWithPermissionsListSerializer,
    GroupWithPermissionsDetailSerializer,
    PermissionSerializer
)
from ..services.group_service import GroupService

User = get_user_model()
logger = logging.getLogger(__name__)


class CreateGroupView(generics.CreateAPIView):
    parser_classes = (JSONParser,)
    queryset = Group.objects.all()
    serializer_class = GroupCreateSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        group = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"Group created successfully: {group.name}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class UpdateGroupView(generics.UpdateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupUpdateSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def put(self, request, *args, **kwargs):
        group = self.get_object()
        serializer = self.get_serializer(group, data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        updated_group = GroupService.update_group(
            group_id=group.id,
            name=validated_data.get('name'),
            description=validated_data.get('description'),
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = self.get_serializer(updated_group)
        logger.info(f"Group updated successfully: {updated_group.name}")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class GetAllGroupsView(generics.ListAPIView):
    serializer_class = GroupListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'
        return GroupService.get_all_groups(include_inactive=include_inactive)


    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} groups")

            return Response({
                'count': len(serializer.data),
                'groups': serializer.data
            }, status=status.HTTP_200_OK)

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting all groups: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving groups",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None


class GetGroupByIdView(generics.RetrieveAPIView):
    serializer_class = GroupDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def get(self, request, *args, **kwargs):
        group = self.get_object()
        serializer = self.get_serializer(group)
        return Response({
            'group': serializer.data
        }, status=status.HTTP_200_OK)


class DeleteGroupView(generics.DestroyAPIView):
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def delete(self, request, *args, **kwargs):
        group = self.get_object()
        GroupService.delete_group(
            group_id=group.id,
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )
        
        logger.info(f"Group deleted successfully: {group.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)


class GetUserGroupsView(generics.ListAPIView):
    serializer_class = GroupListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        user_id = self.kwargs.get('user_id')
        return GroupService.get_user_groups(user_id)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            user_id = self.kwargs.get('user_id')
            logger.info(f"Retrieved {len(serializer.data)} groups for user {user_id}")

            return Response({
                'count': len(serializer.data),
                'groups': serializer.data
            }, status=status.HTTP_200_OK)

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting user groups: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving user groups",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None


class GetAllPermissionsView(generics.ListAPIView):
    """View to get all available permissions in the system"""
    serializer_class = PermissionSerializer
    permission_classes = [AllowAny]
    pagination_class = None

    def get_queryset(self):
        return GroupService.get_all_permissions()

    @extend_schema(
        summary="Get all permissions",
        description="Retrieve all available permissions in the system",
        responses={
            200: OpenApiResponse(
                response=PermissionSerializer(many=True),
                description="Permissions retrieved successfully"
            )
        }
    )
    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} permissions")

            return Response({
                'count': len(serializer.data),
                'permissions': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in getting permissions: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving permissions",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateGroupWithPermissionsView(generics.CreateAPIView):
    """Create a new group with permissions"""
    parser_classes = (JSONParser,)
    queryset = Group.objects.all()
    serializer_class = GroupWithPermissionsCreateSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Create group with permissions",
        description="Create a new group with specified permissions",
        request=GroupWithPermissionsCreateSerializer,
        responses={
            201: OpenApiResponse(
                response=GroupWithPermissionsDetailSerializer,
                description="Group created successfully"
            ),
            400: OpenApiResponse(description="Bad request")
        }
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data

        # For testing purposes, create a dummy user if no authenticated user
        from django.contrib.auth import get_user_model
        User = get_user_model()

        if hasattr(request, 'user') and request.user.is_authenticated:
            created_by = request.user
        else:
            # Create or get a default user for testing
            created_by, _ = User.objects.get_or_create(
                username='default_user',
                defaults={
                    'email': '<EMAIL>',
                    'phone_number': '+1234567890',
                    'first_name': 'Default',
                    'last_name': 'User'
                }
            )

        group = GroupService.create_group_with_permissions(
            name=validated_data['name'],
            permissions=validated_data.get('permissions', []),
            created_by=created_by
        )

        response_serializer = GroupWithPermissionsDetailSerializer(group)
        headers = self.get_success_headers(response_serializer.data)

        logger.info(f"Group with permissions created successfully: {group.name}")

        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class UpdateGroupWithPermissionsView(generics.UpdateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupWithPermissionsUpdateSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)
    def put(self, request, *args, **kwargs):
        group = self.get_object()
        serializer = self.get_serializer(group, data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        updated_group = GroupService.update_group_with_permissions(
            group_id=group.id,
            name=validated_data.get('name'),
            permissions=validated_data.get('permissions'),
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = GroupWithPermissionsDetailSerializer(updated_group)
        logger.info(f"Group with permissions updated successfully: {updated_group.name}")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class GetAllGroupsWithPermissionsView(generics.ListAPIView):
    """Get all groups with permissions"""
    serializer_class = GroupWithPermissionsListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return GroupService.get_all_groups()

    @extend_schema(
        summary="Get all groups with permissions",
        description="Retrieve all active groups with their permissions",
        responses={
            200: OpenApiResponse(
                response=GroupWithPermissionsListSerializer(many=True),
                description="Groups retrieved successfully"
            )
        }
    )
    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} groups with permissions")

            return Response({
                'count': len(serializer.data),
                'groups': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in getting groups with permissions: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving groups",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None


class GetGroupWithPermissionsByIdView(generics.RetrieveAPIView):
    """Get group details with permissions by ID"""
    serializer_class = GroupWithPermissionsDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    @extend_schema(
        summary="Get group details with permissions",
        description="Retrieve detailed information about a specific group including permissions",
        responses={
            200: OpenApiResponse(
                response=GroupWithPermissionsDetailSerializer,
                description="Group details retrieved successfully"
            ),
            404: OpenApiResponse(description="Group not found")
        }
    )
    def get(self, request, *args, **kwargs):
        try:
            group = self.get_object()
            serializer = self.get_serializer(group)

            logger.info(f"Retrieved group details with permissions: {group.name}")

            return Response(
                serializer.data,
                status=status.HTTP_200_OK
            )

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting group details: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving group details",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
