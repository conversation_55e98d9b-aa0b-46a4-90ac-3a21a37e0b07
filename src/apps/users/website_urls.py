from django.urls import path
from .views.user_views import RegisterView
from .views.auth_views import LoginView, LogoutView, TokenRefreshView, VerifyTokenView

# Website URLs - Public and Authenticated User Access (راوتات الموقع العادي للمستخدمين)
urlpatterns = [
    # Authentication Routes (راوتات المصادقة)
    path("auth/login/", LoginView.as_view(), name="website_login"),
    path("auth/logout/", LogoutView.as_view(), name="website_logout"),
    path("auth/refresh/", TokenRefreshView.as_view(), name="website_token_refresh"),
    path("auth/verify/", VerifyTokenView.as_view(), name="website_verify_token"),

    # User Registration Route (راوت التسجيل)
    path("register/", RegisterView.as_view(), name="website_register"),
]
