from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from ..models import Group, GroupMembership
from .user_serializers import GetAllUsersSerializer

User = get_user_model()


class GroupMembershipSerializer(serializers.ModelSerializer):
    user = GetAllUsersSerializer(read_only=True)
    
    class Meta:
        model = GroupMembership
        fields = ['id', 'user', 'role', 'is_active', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class GroupCreateSerializer(serializers.ModelSerializer):
    created_by = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Group
        fields = ['name', 'description','created_by']

    def validate_name(self, value):
        if Group.objects.filter(name=value, is_active=True).exists():
            raise serializers.ValidationError("A group with this name already exists.")
        return value

    def create(self, validated_data):
        # Handle anonymous user case
        user = validated_data.get('created_by')
        if user.is_anonymous:
            # Get or create a default user for testing
            default_user, _ = User.objects.get_or_create(
                username='default_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Default',
                    'last_name': 'User',
                    'phone_number': '+1234567890',
                    'is_active': True
                }
            )
            validated_data['created_by'] = default_user

        group = Group.objects.create(**validated_data)
        return group


class GroupUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Group
        fields = ['name', 'description']
        
    def validate_name(self, value):
        if Group.objects.filter(
            name=value, 
            is_active=True
        ).exclude(id=self.instance.id).exists():
            raise serializers.ValidationError("A group with this name already exists.")
        return value


class GroupListSerializer(serializers.ModelSerializer):
    created_by = GetAllUsersSerializer(read_only=True)
    member_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Group
        fields = [
            'id', 'name', 'description', 'created_by', 
            'member_count', 'is_active', 'date_created'
        ]


class GroupDetailSerializer(serializers.ModelSerializer):
    created_by = GetAllUsersSerializer(read_only=True)
    members = GroupMembershipSerializer(source='groupmembership_set', many=True, read_only=True)
    member_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Group
        fields = [
            'id', 'name', 'description', 'created_by', 
            'members', 'member_count', 'is_active', 
            'date_created', 'date_updated'
        ]


class GroupMemberAddSerializer(serializers.Serializer):
    user_id = serializers.UUIDField()
    role = serializers.ChoiceField(
        choices=GroupMembership.ROLE_CHOICES,
        default='member'
    )
    
    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value, is_active=True)
            return user
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or inactive.")
    
    def validate(self, attrs):
        group = self.context['group']
        user = attrs['user_id']
        
        if GroupMembership.objects.filter(
            group=group, 
            user=user, 
            is_active=True
        ).exists():
            raise serializers.ValidationError("User is already a member of this group.")
        
        return attrs


class GroupMemberUpdateSerializer(serializers.Serializer):
    role = serializers.ChoiceField(choices=GroupMembership.ROLE_CHOICES)


class GroupMemberRemoveSerializer(serializers.Serializer):
    user_id = serializers.UUIDField()
    
    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            return user
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found.")
    
    def validate(self, attrs):
        group = self.context['group']
        user = attrs['user_id']
        
        if not GroupMembership.objects.filter(
            group=group, 
            user=user, 
            is_active=True
        ).exists():
            raise serializers.ValidationError("User is not a member of this group.")
        
        if GroupMembership.objects.filter(
            group=group, 
            user=user, 
            role='admin',
            is_active=True
        ).exists():
            admin_count = GroupMembership.objects.filter(
                group=group, 
                role='admin',
                is_active=True
            ).count()
            if admin_count <= 1:
                raise serializers.ValidationError(
                    "Cannot remove the last admin from the group."
                )
        
        return attrs


class PermissionSerializer(serializers.ModelSerializer):
    """Serializer for Django permissions"""

    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        read_only_fields = ['id', 'name', 'codename', 'content_type']


class GroupWithPermissionsCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating groups with permissions"""
    permissions = serializers.PrimaryKeyRelatedField(
        queryset=Permission.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Group
        fields = ['name', 'permissions']

    def validate_name(self, value):
        if Group.objects.filter(name=value, is_active=True).exists():
            raise serializers.ValidationError("A group with this name already exists.")
        return value

    def create(self, validated_data):
        permissions = validated_data.pop('permissions', [])
        group = Group.objects.create(**validated_data)
        if permissions:
            group.permissions.set(permissions)
        return group


class GroupWithPermissionsUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating groups with permissions"""
    permissions = serializers.PrimaryKeyRelatedField(
        queryset=Permission.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Group
        fields = ['name', 'permissions']

    def validate_name(self, value):
        if Group.objects.filter(
            name=value,
            is_active=True
        ).exclude(id=self.instance.id).exists():
            raise serializers.ValidationError("A group with this name already exists.")
        return value

    def update(self, instance, validated_data):
        permissions = validated_data.pop('permissions', None)

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update permissions if provided
        if permissions is not None:
            instance.permissions.set(permissions)

        return instance


class GroupWithPermissionsListSerializer(serializers.ModelSerializer):
    """Serializer for listing groups with permissions"""
    created_by = GetAllUsersSerializer(read_only=True)
    member_count = serializers.ReadOnlyField()
    permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = Group
        fields = [
            'id', 'name', 'created_by', 'permissions',
            'member_count', 'is_active', 'date_created'
        ]


class GroupWithPermissionsDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed group view with permissions"""
    created_by = GetAllUsersSerializer(read_only=True)
    members = GroupMembershipSerializer(source='groupmembership_set', many=True, read_only=True)
    member_count = serializers.ReadOnlyField()
    permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = Group
        fields = [
            'id', 'name', 'created_by', 'permissions',
            'members', 'member_count', 'is_active',
            'date_created', 'date_updated'
        ]
