from django.urls import path
from .views.website_blog_views import (
    # Public Blog Views
    WebsitePublicBlogsView, WebsiteBlogDetailView, WebsiteBlogsByUserView,
    WebsiteBlogsByCategoryView, WebsiteSearchBlogsView, WebsiteFeaturedBlogsView,
    WebsiteLatestBlogsView, WebsitePopularBlogsView,
    
    # User Blog Management Views (Authenticated Users)
    WebsiteUserBlogsView, WebsiteCreateBlogView, WebsiteUpdateBlogView,
    WebsiteDeleteBlogView, WebsitePublishBlogView, WebsiteUnpublishBlogView,
    WebsiteBlogDraftsView, WebsiteBlogStatsView,
    
    # Blog Interaction Views
    WebsiteLikeBlogView, WebsiteUnlikeBlogView, WebsiteCommentBlogView,
    WebsiteDeleteCommentView, WebsiteShareBlogView,
    
    # Category and Tag Views
    WebsiteAllCategoriesView, WebsiteAllTagsView, WebsiteBlogsByTagView
)

# Website Blog URLs - Public and Authenticated User Access
urlpatterns = [
    # Public Blog Routes (No Authentication Required)
    path("", WebsitePublicBlogsView.as_view(), name="website_public_blogs"),
    path("featured/", WebsiteFeaturedBlogsView.as_view(), name="website_featured_blogs"),
    path("latest/", WebsiteLatestBlogsView.as_view(), name="website_latest_blogs"),
    path("popular/", WebsitePopularBlogsView.as_view(), name="website_popular_blogs"),
    path("search/", WebsiteSearchBlogsView.as_view(), name="website_search_blogs"),
    
    # Blog Detail and User Blogs (Public Access)
    path("<uuid:blog_id>/", WebsiteBlogDetailView.as_view(), name="website_blog_detail"),
    path("user/<uuid:user_id>/", WebsiteBlogsByUserView.as_view(), name="website_blogs_by_user"),
    
    # Category and Tag Routes (Public Access)
    path("categories/", WebsiteAllCategoriesView.as_view(), name="website_all_categories"),
    path("category/<str:category>/", WebsiteBlogsByCategoryView.as_view(), name="website_blogs_by_category"),
    path("tags/", WebsiteAllTagsView.as_view(), name="website_all_tags"),
    path("tag/<str:tag>/", WebsiteBlogsByTagView.as_view(), name="website_blogs_by_tag"),
    
    # User Blog Management Routes (Authenticated Users Only)
    path("my-blogs/", WebsiteUserBlogsView.as_view(), name="website_user_blogs"),
    path("my-blogs/drafts/", WebsiteBlogDraftsView.as_view(), name="website_blog_drafts"),
    path("my-blogs/stats/", WebsiteBlogStatsView.as_view(), name="website_blog_stats"),
    path("my-blogs/create/", WebsiteCreateBlogView.as_view(), name="website_create_blog"),
    path("my-blogs/<uuid:blog_id>/update/", WebsiteUpdateBlogView.as_view(), name="website_update_blog"),
    path("my-blogs/<uuid:blog_id>/delete/", WebsiteDeleteBlogView.as_view(), name="website_delete_blog"),
    path("my-blogs/<uuid:blog_id>/publish/", WebsitePublishBlogView.as_view(), name="website_publish_blog"),
    path("my-blogs/<uuid:blog_id>/unpublish/", WebsiteUnpublishBlogView.as_view(), name="website_unpublish_blog"),
    
    # Blog Interaction Routes (Authenticated Users Only)
    path("<uuid:blog_id>/like/", WebsiteLikeBlogView.as_view(), name="website_like_blog"),
    path("<uuid:blog_id>/unlike/", WebsiteUnlikeBlogView.as_view(), name="website_unlike_blog"),
    path("<uuid:blog_id>/comment/", WebsiteCommentBlogView.as_view(), name="website_comment_blog"),
    path("<uuid:blog_id>/share/", WebsiteShareBlogView.as_view(), name="website_share_blog"),
    path("comments/<uuid:comment_id>/delete/", WebsiteDeleteCommentView.as_view(), name="website_delete_comment"),
]
