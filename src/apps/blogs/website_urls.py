from django.urls import path
from .views.blog_views import (
    CreateBlogView,
    UpdateBlogView,
    GetAllBlogsView,
    GetBlogByIdView,
    GetBlogsByAuthorView,
    DeleteBlogView
)

# Website Blog URLs - Public and Authenticated User Access (راوتات البلوجات للموقع العادي)
urlpatterns = [
    # Public Blog Routes (راوتات البلوجات العامة - بدون مصادقة)
    path("", GetAllBlogsView.as_view(), name="website_get_all_blogs"),
    path("<uuid:id>/", GetBlogByIdView.as_view(), name="website_get_blog_by_id"),
    path("author/<uuid:author_id>/", GetBlogsByAuthorView.as_view(), name="website_get_blogs_by_author"),
    
    # User Blog Management Routes (راوتات إدارة البلوجات للمستخدمين المسجلين)
    path("create/", CreateBlogView.as_view(), name="website_create_blog"),
    path("update/", UpdateBlogView.as_view(), name="website_update_blog"),
    path("delete/<uuid:id>/", DeleteBlogView.as_view(), name="website_delete_blog"),
]
