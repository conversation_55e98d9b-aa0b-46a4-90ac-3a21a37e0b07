from django.urls import path
from .views.blog_views import (
    CreateBlogView,
    UpdateBlogView,
    GetAllBlogsView,
    GetBlogByIdView,
    GetBlogsByAuthorView,
    DeleteBlogView
)

# Dashboard Blog URLs - Admin Only Access (راوتات البلوجات للداش بورد - الأدمن فقط)
urlpatterns = [
    # Blog Management Routes (إدارة البلوجات)
    path("", GetAllBlogsView.as_view(), name="dashboard_get_all_blogs"),
    path("<uuid:id>/", GetBlogByIdView.as_view(), name="dashboard_get_blog_by_id"),
    path("author/<uuid:author_id>/", GetBlogsByAuthorView.as_view(), name="dashboard_get_blogs_by_author"),
    path("create/", CreateBlogView.as_view(), name="dashboard_create_blog"),
    path("update/", UpdateBlogView.as_view(), name="dashboard_update_blog"),
    path("delete/<uuid:id>/", DeleteBlogView.as_view(), name="dashboard_delete_blog"),
]
