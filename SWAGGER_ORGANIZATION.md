# تنظيم Swagger للراوتات - BlogCore

## 📊 التنظيم الجديد في Swagger

تم تقسيم الراوتات إلى مجموعات منطقية منفصلة لأفضل تنظيم في Swagger:

### 🎯 مجموعات الراوتات:

#### 1. **Dashboard Users** (`/api/dashboard/users/`)
- إدارة المستخدمين للأدمن فقط
- عرض، تعديل، وإدارة المستخدمين

#### 2. **Dashboard Groups** (`/api/dashboard/groups/`)
- إدارة المجموعات للأدمن فقط
- إنشاء، تعديل، حذف المجموعات
- ربط المستخدمين بالمجموعات

#### 3. **Dashboard Permissions** (`/api/dashboard/permissions/`)
- إدارة الصلاحيات للأدمن فقط
- عرض وإدارة جميع الصلاحيات

#### 4. **Dashboard Blogs** (`/api/dashboard/blogs/`)
- إدارة البلوجات للأدمن فقط
- عرض، تعديل، حذف جميع البلوجات

#### 5. **Website Users** (`/api/users/`)
- راوتات المستخدمين العاديين
- التسجيل والمصادقة

#### 6. **Website Blogs** (`/api/blogs/`)
- راوتات البلوجات العامة
- عرض وإدارة البلوجات للمستخدمين

## 🔧 مميزات التنظيم الجديد:

### ✅ في Swagger:
1. **تجميع منطقي**: كل قسم في مجموعة منفصلة
2. **سهولة التنقل**: الراوتات مرتبة حسب الوظيفة
3. **وضوح الصلاحيات**: فصل واضح بين راوتات الأدمن والمستخدمين
4. **تنظيم هرمي**: راوتات الداش بورد تحت `/dashboard/`

### ✅ في الكود:
1. **ملفات منفصلة**: كل مجموعة في ملف منفصل
2. **سهولة الصيانة**: تعديل قسم واحد لا يؤثر على الباقي
3. **وضوح المسؤوليات**: كل ملف له غرض محدد
4. **قابلية التوسع**: إضافة راوتات جديدة بسهولة

## 📁 هيكل الملفات:

```
src/BlogCore/urls.py                     # الملف الرئيسي
src/apps/users/
├── dashboard_users_urls.py              # راوتات إدارة المستخدمين
├── dashboard_groups_urls.py             # راوتات إدارة المجموعات
├── dashboard_permissions_urls.py        # راوتات إدارة الصلاحيات
├── website_urls.py                      # راوتات الموقع العادي
└── group_urls.py                        # راوتات قديمة (للتوافق)

src/apps/blogs/
├── dashboard_urls.py                    # راوتات إدارة البلوجات
├── website_urls.py                      # راوتات البلوجات العامة
└── urls.py                              # راوتات قديمة (للتوافق)
```

## 🎨 مظهر Swagger:

### قبل التنظيم:
```
- Users
- Groups  
- Blogs
- Auth
```

### بعد التنظيم:
```
📊 Dashboard
├── 👥 Dashboard Users
├── 👥 Dashboard Groups  
├── 🔑 Dashboard Permissions
└── 📝 Dashboard Blogs

🌐 Website
├── 👤 Website Users
└── 📖 Website Blogs

🔄 Legacy
└── 👥 Groups (Old)
```

## 🚀 الفوائد:

1. **للمطورين**: سهولة العثور على الراوتات المطلوبة
2. **للأدمن**: واجهة واضحة لإدارة النظام
3. **للمستخدمين**: راوتات منفصلة وآمنة
4. **للصيانة**: كود منظم وقابل للتطوير

## 📝 ملاحظات:

- تم الحفاظ على الراوتات القديمة لضمان التوافق
- كل مجموعة راوتات لها صلاحيات محددة
- التنظيم يدعم إضافة مجموعات جديدة بسهولة
- Swagger سيعرض الراوتات بشكل منظم ومرتب
