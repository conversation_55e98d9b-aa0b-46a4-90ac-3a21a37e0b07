# Group Management API Documentation

## نظرة عامة
تم تطوير نظام إدارة المجموعات مع الصلاحيات (Permissions) في Django. يوفر هذا النظام إمكانية إنشاء وإدارة المجموعات مع تخصيص صلاحيات محددة لكل مجموعة.

## 🔗 الراوتات منفصلة في Swagger
تم فصل راوتات المجموعات عن راوتات المستخدمين في Swagger:
- **راوتات المستخدمين**: `/api/users/`
- **راوتات المجموعات**: `/api/groups/`

## الراوتات الجديدة

### 1. إنشاء مجموعة مع صلاحيات
**POST** `/api/groups/with-permissions/`

**الطلب:**
```json
{
    "name": "اسم المجموعة",
    "permissions": [21, 22, 24]  // معرفات الصلاحيات
}
```

**الاستجابة:**
```json
{
    "id": "uuid",
    "name": "اسم المجموعة",
    "created_by": {
        "id": "uuid",
        "username": "username",
        "email": "<EMAIL>",
        "first_name": "الاسم الأول",
        "last_name": "الاسم الأخير",
        "full_name": "الاسم الكامل",
        "image_url": null,
        "phone_number": "+1234567890",
        "date_joined": "2025-09-11T20:09:10.282693Z",
        "is_active": true
    },
    "permissions": [
        {
            "id": 21,
            "name": "Can add User",
            "codename": "add_user",
            "content_type": 6
        }
    ],
    "members": [...],
    "member_count": 1,
    "is_active": true,
    "date_created": "2025-09-11T20:09:10.292096Z",
    "date_updated": "2025-09-11T20:09:10.292106Z"
}
```

### 2. تعديل مجموعة مع صلاحيات
**PUT** `/api/groups/with-permissions/{id}/update/`

**الطلب:**
```json
{
    "name": "اسم المجموعة المحدث",
    "permissions": [21, 23]  // الصلاحيات الجديدة
}
```

### 3. عرض جميع المجموعات مع صلاحيات
**GET** `/api/groups/with-permissions/all/`

**الاستجابة:**
```json
{
    "count": 1,
    "groups": [
        {
            "id": "uuid",
            "name": "اسم المجموعة",
            "created_by": {...},
            "permissions": [...],
            "member_count": 1,
            "is_active": true,
            "date_created": "2025-09-11T20:09:10.292096Z"
        }
    ]
}
```

### 4. عرض تفاصيل مجموعة مع صلاحيات
**GET** `/api/groups/with-permissions/{id}/`

### 5. حذف مجموعة
**DELETE** `/api/groups/{id}/delete/`

### 6. عرض جميع الصلاحيات المتاحة
**GET** `/api/groups/permissions/`

**الاستجابة:**
```json
{
    "count": 44,
    "permissions": [
        {
            "id": 1,
            "name": "Can add log entry",
            "codename": "add_logentry",
            "content_type": 1
        },
        {
            "id": 21,
            "name": "Can add User",
            "codename": "add_user",
            "content_type": 6
        }
    ]
}
```

## الراوتات الأساسية (بدون صلاحيات)
تم فصل راوتات المجموعات الأساسية:

- **POST** `/api/groups/` - إنشاء مجموعة أساسية
- **GET** `/api/groups/all/` - عرض جميع المجموعات الأساسية
- **GET** `/api/groups/{id}/` - عرض تفاصيل مجموعة أساسية
- **PUT** `/api/groups/{id}/update/` - تعديل مجموعة أساسية
- **DELETE** `/api/groups/{id}/delete/` - حذف مجموعة
- **GET** `/api/groups/users/{user_id}/` - عرض مجموعات مستخدم

## راوتات إدارة أعضاء المجموعات
- **GET** `/api/groups/{group_id}/members/` - عرض أعضاء المجموعة
- **POST** `/api/groups/{group_id}/members/add/` - إضافة عضو للمجموعة
- **PUT** `/api/groups/{group_id}/members/{user_id}/update/` - تعديل دور العضو
- **DELETE** `/api/groups/{group_id}/members/{user_id}/remove/` - إزالة عضو من المجموعة
- **DELETE** `/api/groups/{group_id}/members/remove/` - إزالة عضو (بالـ body)

## الميزات الجديدة

### 1. نموذج Group محدث
- إضافة حقل `permissions` من نوع ManyToManyField مع Django Permission model
- دعم ربط الصلاحيات بالمجموعات

### 2. Serializers جديدة
- `PermissionSerializer` - لعرض الصلاحيات
- `GroupWithPermissionsCreateSerializer` - لإنشاء مجموعة مع صلاحيات
- `GroupWithPermissionsUpdateSerializer` - لتعديل مجموعة مع صلاحيات
- `GroupWithPermissionsListSerializer` - لعرض قائمة المجموعات مع صلاحيات
- `GroupWithPermissionsDetailSerializer` - لعرض تفاصيل مجموعة مع صلاحيات

### 3. خدمات محدثة
- `GroupService.create_group_with_permissions()` - إنشاء مجموعة مع صلاحيات
- `GroupService.update_group_with_permissions()` - تعديل مجموعة مع صلاحيات
- `GroupService.get_all_permissions()` - الحصول على جميع الصلاحيات

### 4. Views جديدة
- `CreateGroupWithPermissionsView` - إنشاء مجموعة مع صلاحيات
- `UpdateGroupWithPermissionsView` - تعديل مجموعة مع صلاحيات
- `GetAllGroupsWithPermissionsView` - عرض جميع المجموعات مع صلاحيات
- `GetGroupWithPermissionsByIdView` - عرض تفاصيل مجموعة مع صلاحيات
- `GetAllPermissionsView` - عرض جميع الصلاحيات

## أمثلة الاستخدام

### إنشاء مجموعة مع صلاحيات المستخدمين
```bash
curl -X POST "http://127.0.0.1:8001/api/groups/with-permissions/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "مديرو المستخدمين",
    "permissions": [21, 22, 23, 24]
  }'
```

### إنشاء مجموعة أساسية
```bash
curl -X POST "http://127.0.0.1:8001/api/groups/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "مجموعة أساسية",
    "description": "وصف المجموعة"
  }'
```

### تعديل صلاحيات مجموعة
```bash
curl -X PUT "http://127.0.0.1:8001/api/groups/with-permissions/{group_id}/update/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "مديرو المستخدمين المحدث",
    "permissions": [21, 24]
  }'
```

### عرض جميع الصلاحيات المتاحة
```bash
curl -X GET "http://127.0.0.1:8001/api/groups/permissions/"
```

## ملاحظات مهمة

1. **الصلاحيات ثابتة**: الصلاحيات في النظام ثابتة ولا يمكن تعديلها أو حذفها، فقط عرضها
2. **المصادقة**: حالياً النظام يدعم العمل بدون مصادقة للاختبار
3. **المستخدم الافتراضي**: يتم إنشاء مستخدم افتراضي تلقائياً عند عدم وجود مستخدم مصادق عليه
4. **التوافق**: الراوتات الأساسية للمجموعات ما زالت تعمل بشكل طبيعي

## قاعدة البيانات

تم إنشاء migration جديد:
- `0003_group_permissions.py` - إضافة حقل permissions للمجموعات

## الاختبار

جميع الراوتات تم اختبارها وتعمل بشكل صحيح:
- ✅ إنشاء مجموعة مع صلاحيات
- ✅ تعديل مجموعة مع صلاحيات  
- ✅ عرض جميع المجموعات مع صلاحيات
- ✅ عرض تفاصيل مجموعة مع صلاحيات
- ✅ حذف مجموعة
- ✅ عرض جميع الصلاحيات المتاحة
