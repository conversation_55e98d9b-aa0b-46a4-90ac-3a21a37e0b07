# هيكل الراوتات الجديد - BlogCore

تم إعادة تنظيم الراوتات لفصل صلاحيات الأدمن عن المستخدمين العاديين مع تنظيم أفضل في Swagger.

## 🔧 راوتات الداش بورد (للأدمن فقط)

### 👥 راوتات إدارة المستخدمين
```
/api/dashboard/users/                           # عرض جميع المستخدمين
/api/dashboard/users/<uuid:id>/                 # عرض مستخدم محدد
/api/dashboard/users/<uuid:id>/update/          # تعديل مستخدم
```

### 👥 راوتات إدارة المجموعات
```
/api/dashboard/groups/                          # عرض جميع المجموعات
/api/dashboard/groups/create/                   # إنشاء مجموعة جديدة
/api/dashboard/groups/<uuid:id>/                # عرض مجموعة محددة
/api/dashboard/groups/<uuid:id>/update/         # تعديل مجموعة
/api/dashboard/groups/<uuid:id>/delete/         # حذف مجموعة
```

### 🔐 راوتات إدارة المجموعات مع الصلاحيات
```
/api/dashboard/groups/with-permissions/                    # إنشاء مجموعة مع صلاحيات
/api/dashboard/groups/with-permissions/all/               # عرض جميع المجموعات مع صلاحياتها
/api/dashboard/groups/with-permissions/<uuid:id>/         # عرض مجموعة مع صلاحياتها
/api/dashboard/groups/with-permissions/<uuid:id>/update/  # تعديل مجموعة وصلاحياتها
```

### 🔑 راوتات إدارة الصلاحيات
```
/api/dashboard/permissions/                     # عرض جميع الصلاحيات
```

### 📝 راوتات إدارة البلوجات (للأدمن)
```
/api/dashboard/blogs/                           # عرض جميع البلوجات
/api/dashboard/blogs/<uuid:id>/                 # عرض بلوج محدد
/api/dashboard/blogs/author/<uuid:author_id>/   # عرض بلوجات كاتب محدد
/api/dashboard/blogs/create/                    # إنشاء بلوج جديد
/api/dashboard/blogs/update/                    # تعديل بلوج
/api/dashboard/blogs/delete/<uuid:id>/          # حذف بلوج
```

### 🔗 راوتات ربط المستخدمين بالمجموعات
```
/api/dashboard/groups/users/<uuid:user_id>/     # عرض مجموعات مستخدم محدد
```

## 👥 راوتات الموقع العادي (للمستخدمين)

### راوتات المصادقة
```
/api/users/auth/login/                          # تسجيل الدخول
/api/users/auth/logout/                         # تسجيل الخروج
/api/users/auth/refresh/                        # تحديث التوكن
/api/users/auth/verify/                         # التحقق من التوكن
/api/users/register/                            # التسجيل
```

### راوتات البلوجات (للمستخدمين العاديين)
```
/api/blogs/                                     # عرض جميع البلوجات العامة
/api/blogs/<uuid:id>/                           # عرض بلوج محدد
/api/blogs/author/<uuid:author_id>/             # عرض بلوجات كاتب محدد
/api/blogs/create/                              # إنشاء بلوج جديد (للمستخدمين المسجلين)
/api/blogs/update/                              # تعديل بلوج (للمالك فقط)
/api/blogs/delete/<uuid:id>/                    # حذف بلوج (للمالك فقط)
```

## 🔄 راوتات قديمة (للتوافق)
```
/api/groups/                                    # راوتات المجموعات القديمة
```

## 📊 تنظيم Swagger

### 🎯 مجموعات الراوتات في Swagger:
1. **Dashboard Users** - إدارة المستخدمين
2. **Dashboard Groups** - إدارة المجموعات
3. **Dashboard Permissions** - إدارة الصلاحيات
4. **Dashboard Blogs** - إدارة البلوجات
5. **Website Users** - راوتات المستخدمين العاديين
6. **Website Blogs** - راوتات البلوجات العامة

## 🔐 الصلاحيات

### راوتات الداش بورد
- **الوصول**: الأدمن فقط
- **الصلاحيات**: إدارة كاملة للمستخدمين والمجموعات والبلوجات
- **المميزات**:
  - عرض وتعديل جميع المستخدمين
  - إدارة المجموعات والصلاحيات
  - إدارة جميع البلوجات

### راوتات الموقع العادي
- **الوصول**: عام ومستخدمين مسجلين
- **الصلاحيات محدودة**:
  - المستخدمون العاديون: عرض البلوجات العامة فقط
  - المستخدمون المسجلون: إنشاء وتعديل بلوجاتهم الخاصة
  - لا يمكن الوصول لإدارة المستخدمين أو المجموعات

## 📝 ملاحظات مهمة

1. **فصل الصلاحيات**: تم فصل راوتات الأدمن عن المستخدمين العاديين بشكل كامل
2. **الأمان**: راوتات الداش بورد محمية بصلاحيات الأدمن فقط
3. **التوافق**: تم الحفاظ على الراوتات القديمة لضمان عدم كسر الكود الموجود
4. **التنظيم المحسن**: كل قسم في ملف منفصل لأفضل تنظيم في Swagger
5. **سهولة الصيانة**: كل نوع من الراوتات في ملف منفصل لسهولة الصيانة

## 🚀 كيفية الاستخدام

### للأدمن:
```python
# إدارة المستخدمين
GET /api/dashboard/users/                       # عرض جميع المستخدمين
GET /api/dashboard/users/<uuid:id>/             # عرض مستخدم محدد
PUT /api/dashboard/users/<uuid:id>/update/      # تعديل مستخدم

# إدارة المجموعات
GET /api/dashboard/groups/                      # عرض جميع المجموعات
POST /api/dashboard/groups/create/              # إنشاء مجموعة جديدة
PUT /api/dashboard/groups/<uuid:id>/update/     # تعديل مجموعة

# إدارة الصلاحيات
GET /api/dashboard/permissions/                 # عرض جميع الصلاحيات
PUT /api/dashboard/groups/with-permissions/<id>/update/  # تعديل صلاحيات مجموعة

# إدارة البلوجات
GET /api/dashboard/blogs/                       # عرض جميع البلوجات
DELETE /api/dashboard/blogs/delete/<uuid:id>/   # حذف بلوج
```

### للمستخدمين العاديين:
```python
# المصادقة والتسجيل
POST /api/users/register/                       # التسجيل
POST /api/users/auth/login/                     # تسجيل الدخول

# البلوجات
GET /api/blogs/                                 # عرض البلوجات العامة
GET /api/blogs/<uuid:id>/                       # عرض بلوج محدد
POST /api/blogs/create/                         # إنشاء بلوج جديد (للمسجلين)
```

## 📁 هيكل الملفات الجديد

```
src/apps/users/
├── dashboard_users_urls.py      # راوتات إدارة المستخدمين
├── dashboard_groups_urls.py     # راوتات إدارة المجموعات
├── dashboard_permissions_urls.py # راوتات إدارة الصلاحيات
├── website_urls.py              # راوتات الموقع العادي
└── group_urls.py                # راوتات قديمة (للتوافق)

src/apps/blogs/
├── dashboard_urls.py            # راوتات إدارة البلوجات
├── website_urls.py              # راوتات البلوجات العامة
└── urls.py                      # راوتات قديمة (للتوافق)
```
