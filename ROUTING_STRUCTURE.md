# هيكل الراوتات الجديد - BlogCore

تم إعادة تنظيم الراوتات لفصل صلاحيات الأدمن عن المستخدمين العاديين.

## 🔧 راوتات الداش بورد (للأدمن فقط)

### راوتات إدارة المستخدمين
```
/api/dashboard/users/users/                     # عرض جميع المستخدمين
/api/dashboard/users/users/<uuid:id>/           # عرض مستخدم محدد
/api/dashboard/users/users/<uuid:id>/update/    # تعديل مستخدم
```

### راوتات إدارة المجموعات
```
/api/dashboard/users/groups/                    # عرض جميع المجموعات
/api/dashboard/users/groups/create/             # إنشاء مجموعة جديدة
/api/dashboard/users/groups/<uuid:id>/          # عرض مجموعة محددة
/api/dashboard/users/groups/<uuid:id>/update/   # تعديل مجموعة
/api/dashboard/users/groups/<uuid:id>/delete/   # حذف مجموعة
```

### راوتات إدارة المجموعات مع الصلاحيات
```
/api/dashboard/users/groups/with-permissions/                    # إنشاء مجموعة مع صلاحيات
/api/dashboard/users/groups/with-permissions/all/               # عرض جميع المجموعات مع صلاحياتها
/api/dashboard/users/groups/with-permissions/<uuid:id>/         # عرض مجموعة مع صلاحياتها
/api/dashboard/users/groups/with-permissions/<uuid:id>/update/  # تعديل مجموعة وصلاحياتها
```

### راوتات إدارة الصلاحيات
```
/api/dashboard/users/permissions/               # عرض جميع الصلاحيات
/api/dashboard/users/users/<uuid:user_id>/groups/  # عرض مجموعات مستخدم محدد
```

### راوتات إدارة البلوجات (للأدمن)
```
/api/dashboard/blogs/                           # عرض جميع البلوجات
/api/dashboard/blogs/<uuid:id>/                 # عرض بلوج محدد
/api/dashboard/blogs/author/<uuid:author_id>/   # عرض بلوجات كاتب محدد
/api/dashboard/blogs/create/                    # إنشاء بلوج جديد
/api/dashboard/blogs/update/                    # تعديل بلوج
/api/dashboard/blogs/delete/<uuid:id>/          # حذف بلوج
```

## 👥 راوتات الموقع العادي (للمستخدمين)

### راوتات المصادقة
```
/api/users/auth/login/                          # تسجيل الدخول
/api/users/auth/logout/                         # تسجيل الخروج
/api/users/auth/refresh/                        # تحديث التوكن
/api/users/auth/verify/                         # التحقق من التوكن
/api/users/register/                            # التسجيل
```

### راوتات البلوجات (للمستخدمين العاديين)
```
/api/blogs/                                     # عرض جميع البلوجات العامة
/api/blogs/<uuid:id>/                           # عرض بلوج محدد
/api/blogs/author/<uuid:author_id>/             # عرض بلوجات كاتب محدد
/api/blogs/create/                              # إنشاء بلوج جديد (للمستخدمين المسجلين)
/api/blogs/update/                              # تعديل بلوج (للمالك فقط)
/api/blogs/delete/<uuid:id>/                    # حذف بلوج (للمالك فقط)
```

## 🔄 راوتات قديمة (للتوافق)
```
/api/groups/                                    # راوتات المجموعات القديمة
```

## 🔐 الصلاحيات

### راوتات الداش بورد
- **الوصول**: الأدمن فقط
- **الصلاحيات**: إدارة كاملة للمستخدمين والمجموعات والبلوجات
- **المميزات**: 
  - عرض وتعديل جميع المستخدمين
  - إدارة المجموعات والصلاحيات
  - إدارة جميع البلوجات

### راوتات الموقع العادي
- **الوصول**: عام ومستخدمين مسجلين
- **الصلاحيات محدودة**:
  - المستخدمون العاديون: عرض البلوجات العامة فقط
  - المستخدمون المسجلون: إنشاء وتعديل بلوجاتهم الخاصة
  - لا يمكن الوصول لإدارة المستخدمين أو المجموعات

## 📝 ملاحظات مهمة

1. **فصل الصلاحيات**: تم فصل راوتات الأدمن عن المستخدمين العاديين بشكل كامل
2. **الأمان**: راوتات الداش بورد محمية بصلاحيات الأدمن فقط
3. **التوافق**: تم الحفاظ على الراوتات القديمة لضمان عدم كسر الكود الموجود
4. **التنظيم**: كل نوع من الراوتات في ملف منفصل لسهولة الصيانة

## 🚀 كيفية الاستخدام

### للأدمن:
```python
# الوصول لإدارة المستخدمين
GET /api/dashboard/users/users/

# إنشاء مجموعة جديدة
POST /api/dashboard/users/groups/create/

# تعديل صلاحيات مجموعة
PUT /api/dashboard/users/groups/with-permissions/<id>/update/
```

### للمستخدمين العاديين:
```python
# التسجيل
POST /api/users/register/

# عرض البلوجات
GET /api/blogs/

# إنشاء بلوج جديد
POST /api/blogs/create/
```
